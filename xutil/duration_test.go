// ParseDuration 测试文件
//
// 本文件包含对 xutil.ParseDuration 函数的全面测试，包括：
// 1. 官方兼容性测试 - 确保与 Go 标准库 time.ParseDuration 的兼容性
// 2. Day 和 Week 单位扩展测试 - 测试新增的 "d" 和 "w" 单位支持
// 3. 错误处理测试 - 测试各种无效输入的错误处理
// 4. 边界值测试 - 测试边界情况和特殊值
// 5. 兼容性验证 - 验证与标准库的一致性
package xutil

import (
	"strings"
	"testing"
	"time"
)

// TestParseDuration 测试 ParseDuration 函数的正向用例
func TestParseDuration(t *testing.T) {
	// 官方兼容性测试用例 - 移植自 Go 官方 time 包测试
	var parseDurationTests = []struct {
		in   string
		want time.Duration
	}{
		// simple
		{"0", 0},
		{"5s", 5 * time.Second},
		{"30s", 30 * time.Second},
		{"1478s", 1478 * time.Second},
		// sign
		{"-5s", -5 * time.Second},
		{"+5s", 5 * time.Second},
		{"-0", 0},
		{"+0", 0},
		// decimal
		{"5.0s", 5 * time.Second},
		{"5.6s", 5*time.Second + 600*time.Millisecond},
		{"5.s", 5 * time.Second},
		{".5s", 500 * time.Millisecond},
		{"1.0s", 1 * time.Second},
		{"1.00s", 1 * time.Second},
		{"1.004s", 1*time.Second + 4*time.Millisecond},
		{"1.0040s", 1*time.Second + 4*time.Millisecond},
		{"100.00100s", 100*time.Second + 1*time.Millisecond},
		// different units
		{"10ns", 10 * time.Nanosecond},
		{"11us", 11 * time.Microsecond},
		{"12µs", 12 * time.Microsecond}, // U+00B5
		{"12μs", 12 * time.Microsecond}, // U+03BC
		{"13ms", 13 * time.Millisecond},
		{"14s", 14 * time.Second},
		{"15m", 15 * time.Minute},
		{"16h", 16 * time.Hour},
		// composite durations
		{"3h30m", 3*time.Hour + 30*time.Minute},
		{"10.5s4m", 4*time.Minute + 10*time.Second + 500*time.Millisecond},
		{"-2m3.4s", -(2*time.Minute + 3*time.Second + 400*time.Millisecond)},
		{"1h2m3s4ms5us6ns", 1*time.Hour + 2*time.Minute + 3*time.Second + 4*time.Millisecond + 5*time.Microsecond + 6*time.Nanosecond},
		{"39h9m14.425s", 39*time.Hour + 9*time.Minute + 14*time.Second + 425*time.Millisecond},
		// large value
		{"52763797000ns", 52763797000 * time.Nanosecond},
		// more than 9 digits after decimal point, see https://golang.org/issue/6617
		{"0.3333333333333333333h", 20 * time.Minute},
		// 9007199254740993 = 1<<53+1 cannot be stored precisely in a float64
		{"9007199254740993ns", (1<<53 + 1) * time.Nanosecond},
		// largest duration that can be represented by int64 in nanoseconds
		{"9223372036854775807ns", (1<<63 - 1) * time.Nanosecond},
		{"9223372036854775.807us", (1<<63 - 1) * time.Nanosecond},
		{"9223372036s854ms775us807ns", (1<<63 - 1) * time.Nanosecond},
		{"-9223372036854775808ns", -1 << 63 * time.Nanosecond},
		{"-9223372036854775.808us", -1 << 63 * time.Nanosecond},
		{"-9223372036s854ms775us808ns", -1 << 63 * time.Nanosecond},
		// largest negative value
		{"-9223372036854775808ns", -1 << 63 * time.Nanosecond},
		// largest negative round trip value, see https://golang.org/issue/48629
		{"-2562047h47m16.854775808s", -1 << 63 * time.Nanosecond},
		// huge string; issue 15011.
		{"0.100000000000000000000h", 6 * time.Minute},
		// This value tests the first overflow check in leadingFraction.
		{"0.830103483285477580700h", 49*time.Minute + 48*time.Second + 372539827*time.Nanosecond},
	}

	// 测试官方兼容性用例
	for _, tc := range parseDurationTests {
		d, err := ParseDuration(tc.in)
		if err != nil || d != tc.want {
			t.Errorf("ParseDuration(%q) = %v, %v, want %v, nil", tc.in, d, err, tc.want)
		}
	}

	// Day 和 Week 单位扩展测试用例
	var parseDurationDayWeekTests = []struct {
		in   string
		want time.Duration
	}{
		// Day unit tests
		{"1d", 24 * time.Hour},
		{"2d", 48 * time.Hour},
		{"7d", 7 * 24 * time.Hour},
		{"0.5d", 12 * time.Hour},
		{"1.5d", 36 * time.Hour},
		{"0.25d", 6 * time.Hour},
		{"2.75d", 66 * time.Hour},
		{"-1d", -24 * time.Hour},
		{"-2.5d", -60 * time.Hour},
		{"+3d", 72 * time.Hour},

		// Week unit tests
		{"1w", 7 * 24 * time.Hour},
		{"2w", 14 * 24 * time.Hour},
		{"0.5w", 84 * time.Hour},  // 3.5 days
		{"1.5w", 252 * time.Hour}, // 10.5 days
		{"-1w", -7 * 24 * time.Hour},
		{"-2w", -14 * 24 * time.Hour},
		{"+1w", 7 * 24 * time.Hour},

		// Composite durations with days and weeks
		{"1w1d", 8 * 24 * time.Hour},
		{"2w3d", 17 * 24 * time.Hour},
		{"1w2d3h", 7*24*time.Hour + 2*24*time.Hour + 3*time.Hour},
		{"1w1d1h1m1s", 7*24*time.Hour + 24*time.Hour + time.Hour + time.Minute + time.Second},
		{"2w1d12h30m45s", 14*24*time.Hour + 24*time.Hour + 12*time.Hour + 30*time.Minute + 45*time.Second},

		// Mixed with standard units
		{"1d12h", 36 * time.Hour},
		{"1d30m", 24*time.Hour + 30*time.Minute},
		{"1d1h1m1s1ms1us1ns", 24*time.Hour + time.Hour + time.Minute + time.Second + time.Millisecond + time.Microsecond + time.Nanosecond},
		{"1w1d1h1m1s1ms1us1ns", 7*24*time.Hour + 24*time.Hour + time.Hour + time.Minute + time.Second + time.Millisecond + time.Microsecond + time.Nanosecond},

		// Decimal combinations
		{"1.5d12h", 48 * time.Hour},
		{"0.5w2d", (3.5 + 2) * 24 * time.Hour},      // 0.5w = 3.5d, plus 2d = 5.5d
		{"2.5w1.5d", (17.5 + 1.5) * 24 * time.Hour}, // 2.5w = 17.5d, plus 1.5d = 19d
	}

	// 测试 Day 和 Week 扩展用例
	for _, tc := range parseDurationDayWeekTests {
		d, err := ParseDuration(tc.in)
		if err != nil || d != tc.want {
			t.Errorf("ParseDuration(%q) = %v, %v, want %v, nil", tc.in, d, err, tc.want)
		}
	}
}

// TestParseDurationErrors 测试 ParseDuration 函数的错误处理
func TestParseDurationErrors(t *testing.T) {
	// 错误测试用例
	var parseDurationErrorTests = []struct {
		in     string
		expect string
	}{
		// invalid
		{"", `""`},
		{"3", `"3"`},
		{"-", `"-"`},
		{"s", `"s"`},
		{".", `"."`},
		{"-.", `"-."`},
		{".s", `".s"`},
		{"+.s", `"+.s"`},
		{"\\x85\\x85", `"\\x85\\x85"`},
		{"\\xffff", `"\\xffff"`},
		{"hello \\xffff world", `"hello \\xffff world"`},
		{"\\uFFFD", `"\\uFFFD"`},                                         // utf8.RuneError
		{"\\uFFFD hello \\uFFFD world", `"\\uFFFD hello \\uFFFD world"`}, // utf8.RuneError
		// overflow
		{"9223372036854775810ns", `"9223372036854775810ns"`},
		{"9223372036854775808ns", `"9223372036854775808ns"`},
		{"-9223372036854775809ns", `"-9223372036854775809ns"`},
		{"9223372036854776us", `"9223372036854776us"`},
		{"3000000h", `"3000000h"`},
		{"9223372036854775.808us", `"9223372036854775.808us"`},
		{"9223372036854ms775us808ns", `"9223372036854ms775us808ns"`},
	}

	for _, tc := range parseDurationErrorTests {
		_, err := ParseDuration(tc.in)
		if err == nil {
			t.Errorf("ParseDuration(%q) = _, nil, want _, non-nil", tc.in)
		} else if !strings.Contains(err.Error(), tc.expect) {
			t.Errorf("ParseDuration(%q) = _, %q, error does not contain %q", tc.in, err, tc.expect)
		}
	}
}

// TestParseDurationDayWeekSupport 专门测试 Day 和 Week 单位支持
func TestParseDurationDayWeekSupport(t *testing.T) {
	tests := []struct {
		name string
		in   string
		want time.Duration
	}{
		{"单天", "1d", 24 * time.Hour},
		{"多天", "5d", 5 * 24 * time.Hour},
		{"小数天", "1.5d", 36 * time.Hour},
		{"负数天", "-2d", -48 * time.Hour},
		{"单周", "1w", 7 * 24 * time.Hour},
		{"多周", "3w", 21 * 24 * time.Hour},
		{"小数周", "0.5w", 84 * time.Hour},
		{"负数周", "-1w", -168 * time.Hour},
		{"周天组合", "1w2d", 9 * 24 * time.Hour},
		{"复杂组合", "2w3d4h5m6s", 2*7*24*time.Hour + 3*24*time.Hour + 4*time.Hour + 5*time.Minute + 6*time.Second},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseDuration(tt.in)
			if err != nil {
				t.Errorf("ParseDuration(%q) error = %v, want nil", tt.in, err)
				return
			}
			if got != tt.want {
				t.Errorf("ParseDuration(%q) = %v, want %v", tt.in, got, tt.want)
			}
		})
	}
}

// TestParseDurationBoundaryValues 测试边界值情况
func TestParseDurationBoundaryValues(t *testing.T) {
	tests := []struct {
		name string
		in   string
		want time.Duration
	}{
		{"零值", "0", 0},
		{"零天", "0d", 0},
		{"零周", "0w", 0},
		{"最小正值纳秒", "1ns", time.Nanosecond},
		{"最小正值天", "0.000000000011574d", 1 * time.Nanosecond}, // 约等于 1ns
		{"一年天数", "365d", 365 * 24 * time.Hour},
		{"一年周数", "52w", 52 * 7 * 24 * time.Hour},
		{"闰年天数", "366d", 366 * 24 * time.Hour},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseDuration(tt.in)
			if err != nil {
				t.Errorf("ParseDuration(%q) error = %v, want nil", tt.in, err)
				return
			}
			// 对于极小值，允许一定的精度误差
			if tt.name == "最小正值天" {
				if got <= 0 || got > 1000*time.Nanosecond {
					t.Errorf("ParseDuration(%q) = %v, want approximately %v", tt.in, got, tt.want)
				}
			} else if got != tt.want {
				t.Errorf("ParseDuration(%q) = %v, want %v", tt.in, got, tt.want)
			}
		})
	}
}

// TestParseDurationCompatibility 测试与标准库的兼容性
func TestParseDurationCompatibility(t *testing.T) {
	// 测试所有标准库支持的格式在我们的实现中也能正确解析
	standardTests := []string{
		"0",
		"1ns", "1us", "1µs", "1ms", "1s", "1m", "1h",
		"1.5s", "2m30s", "1h30m", "1h30m45s",
		"-1s", "+1s",
		"72h3m0.5s",
	}

	for _, test := range standardTests {
		t.Run("标准格式_"+test, func(t *testing.T) {
			stdResult, stdErr := time.ParseDuration(test)
			ourResult, ourErr := ParseDuration(test)

			if stdErr != nil && ourErr == nil {
				t.Errorf("标准库解析失败但我们的实现成功: %q, 标准库错误: %v", test, stdErr)
			} else if stdErr == nil && ourErr != nil {
				t.Errorf("标准库解析成功但我们的实现失败: %q, 我们的错误: %v", test, ourErr)
			} else if stdErr == nil && ourErr == nil && stdResult != ourResult {
				t.Errorf("解析结果不一致: %q, 标准库: %v, 我们的: %v", test, stdResult, ourResult)
			}
		})
	}
}

// TestParseDurationOverflowAndPrecision 测试大数值溢出和精度问题
func TestParseDurationOverflowAndPrecision(t *testing.T) {
	// 测试溢出情况
	overflowTests := []string{
		"9223372036854775808ns", // 超过 int64 最大值
		"153722867280912w",      // 大周数可能溢出
		"1000000000000000d",     // 大天数可能溢出
		"999999999999999w999999999999999d999999999999999h", // 组合大数值
	}

	for _, test := range overflowTests {
		t.Run("溢出测试_"+test, func(t *testing.T) {
			_, err := ParseDuration(test)
			if err == nil {
				t.Errorf("ParseDuration(%q) 应该返回溢出错误，但返回了 nil", test)
			} else if !strings.Contains(err.Error(), "invalid duration") {
				t.Errorf("ParseDuration(%q) 错误信息不正确: %v", test, err)
			}
		})
	}

	// 测试精度边界情况
	precisionTests := []struct {
		name string
		in   string
		// 不验证精确值，只验证是否能正确解析且结果合理
	}{
		{"小数周精度", "0.123456789123456789w"},
		{"小数天精度", "0.987654321987654321d"},
		{"极小小数周", "0.000000001w"},
		{"极小小数天", "0.000000001d"},
	}

	for _, tt := range precisionTests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ParseDuration(tt.in)
			if err != nil {
				t.Errorf("ParseDuration(%q) 应该成功解析，但返回错误: %v", tt.in, err)
				return
			}
			if result <= 0 {
				t.Errorf("ParseDuration(%q) 返回了非正数结果: %v", tt.in, result)
			}
			// 验证结果在合理范围内（不为零，不会过大）
			if result > time.Duration(1<<62) {
				t.Errorf("ParseDuration(%q) 返回了过大的结果: %v", tt.in, result)
			}
		})
	}
}
