package xtrace

import (
	"os"

	"work.ctyun.cn/git/public/apm-sdk-go-provider/provider"
	"work.ctyun.cn/git/public/xone/xutil"
)

const (
	XTraceConfigKey = "XTrace"

	defaultTraceReportAddr = "http://127.0.0.1:54318"
)

type Config struct {

	// Addr Trace的上报地址，如果配置了则上报地址为Addr，否则从环境变量"TRACE_ENDPOINT"获取，如果仍获取不到则默认"http://127.0.0.1:54318"
	// optional default "http://127.0.0.1:54318"
	Addr *string

	// Trace是否开启
	// optional default true
	Enable *bool

	// ExtractTraceKey 自定义解析上游 traceid 的 key 命名规则
	// optional default traceid
	ExtractTraceKey *string

	// ExtractTraceKey 自定义传递下游 traceid 的 key 命名规则
	// optional default traceid
	InjectTraceKey *string

	// ServiceName 和 ServiceVersion 优先从 application.yaml 中的 AppID 和 Version 中获取
	// 其次从环境变量 TRACE_SERVICE_NAME 和 TRACE_SERVICE_VERSION 中获取
}

func configMergeDefault(c *Config) *Config {
	if c == nil {
		c = &Config{}
	}
	if c.Addr == nil || *c.Addr == "" {
		if ep := os.Getenv(provider.ENV_TRACE_ENDPOINT); ep != "" {
			c.Addr = xutil.ToPrt(ep)
		} else {
			c.Addr = xutil.ToPrt(defaultTraceReportAddr)
		}
	}
	if c.Enable == nil {
		c.Enable = xutil.ToPrt(true)
	}
	return c
}
