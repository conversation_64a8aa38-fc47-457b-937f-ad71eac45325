package xtrace

import (
	"context"
	"fmt"
	"os"
	"strings"

	"work.ctyun.cn/git/public/xone/xconfig"
	"work.ctyun.cn/git/public/xone/xhook"
	"work.ctyun.cn/git/public/xone/xutil"

	"github.com/spf13/cast"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
	"go.opentelemetry.io/otel/sdk/resource"
	"go.opentelemetry.io/otel/sdk/trace"
	"work.ctyun.cn/git/public/apm-sdk-go-provider/provider"
)

var xTraceShutdownFunc func() error

func init() {
	xhook.BeforeStart(initXTrace)
	xhook.BeforeStop(shutdownXTrace)
}

func initXTrace() error {
	c, err := getConfig()
	if err != nil {
		return fmt.Errorf("XOne initXTrace getConfig failed, err=[%v]", err)
	}

	if !cast.ToBool(c.Enable) {
		xutil.InfoIfEnableDebug("XOne initXTrace ignored, because of config XTrace.Enable=false")
		return nil
	}

	serviceName := xconfig.GetServerAppId()
	serviceVersion := xconfig.GetServerVersion()

	if serviceName == "" {
		if serviceName = os.Getenv(provider.ENV_TRACE_SERVICE_NAME); serviceName == "" {
			return fmt.Errorf("XOne initXTrace failed, Server.AppID should not be empty")
		}
	}
	if serviceVersion == "" {
		serviceVersion = os.Getenv(provider.ENV_TRACE_SERVICE_VERSION)
	}

	xutil.InfoIfEnableDebug("XOne initXTrace got param: ServiceName:%s, ServiceVersion:%s, ExporterEndpoint:%s", serviceName, serviceVersion, cast.ToString(c.Addr))

	if enableConsolePrintTracePrint() {
		return initDebugXTraceByConfig(c, serviceName, serviceVersion)
	}

	return initXTraceByConfig(c, serviceName, serviceVersion)
}

func initXTraceByConfig(c *Config, serviceName, serviceVersion string) error {
	opts := []provider.Option{
		provider.WithServiceName(serviceName),
		provider.WithServiceVersion(serviceVersion),
		provider.WithTraceExporterEndpoint(cast.ToString(c.Addr)),
	}
	if c.ExtractTraceKey != nil {
		opts = append(opts, provider.WithTraceidExtractHeader(*c.ExtractTraceKey))
	}
	if c.InjectTraceKey != nil {
		opts = append(opts, provider.WithTraceidInjectHeader(*c.InjectTraceKey))
	}

	shutdownFunc, err := provider.Init(context.Background(), opts...)

	if err != nil {
		return fmt.Errorf("XOne initXTraceByConfig invoke provider.Init failed, err=[%v]", err)
	}

	xTraceShutdownFunc = func() error {
		shutdownFunc()
		return nil
	}

	return nil
}

func initDebugXTraceByConfig(c *Config, serviceName, serviceVersion string) error {
	console, err := stdouttrace.New(stdouttrace.WithPrettyPrint())
	if err != nil {
		return fmt.Errorf("XOne initDebugXTraceByConfig invoke stdouttrace.New failed, err=[%v]", err)
	}

	r, err := resource.New(
		context.Background(),
		resource.WithFromEnv(),
		resource.WithProcess(),
		resource.WithTelemetrySDK(),
		resource.WithHost(),
		resource.WithAttributes(
			attribute.String("serviceName", serviceName),
			attribute.String("serviceVersion", serviceVersion),
		),
	)
	if err != nil {
		return fmt.Errorf("XOne initDebugXTraceByConfig invoke resource.New failed, err=[%v]", err)
	}

	tp := trace.NewTracerProvider(
		trace.WithBatcher(console),
		trace.WithSampler(trace.AlwaysSample()),
		trace.WithResource(r),
	)
	otel.SetTracerProvider(tp)

	xTraceShutdownFunc = func() error {
		return tp.Shutdown(context.Background())
	}

	return nil
}

func getConfig() (*Config, error) {
	c := &Config{}
	if err := xconfig.UnmarshalConfig(XTraceConfigKey, c); err != nil {
		return nil, err
	}
	c = configMergeDefault(c)
	return c, nil
}

func shutdownXTrace() error {
	if xTraceShutdownFunc != nil {
		return xTraceShutdownFunc()
	}
	return nil
}

// 启动控制台答应trace上报内容，本地调试用
func enableConsolePrintTracePrint() bool {
	return strings.ToLower(os.Getenv("ENABLE_CONSOLE_PRINT_TRACE")) == "true"
}
