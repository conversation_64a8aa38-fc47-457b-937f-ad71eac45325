package xlog

import (
	"fmt"
	"io"
	"os"
	"path"
	"runtime"
	"strings"
	"time"

	"work.ctyun.cn/git/public/xone/xconfig"
	"work.ctyun.cn/git/public/xone/xhook"
	"work.ctyun.cn/git/public/xone/xutil"

	rotatelogs "github.com/lestrrat-go/file-rotatelogs"
	"github.com/sirupsen/logrus"
	logwriter "github.com/sirupsen/logrus/hooks/writer"
	"github.com/spf13/cast"
)

const (
	findFrameIgnoreFileName = "/xlog/util.go"
)

func init() {
	xhook.BeforeStart(initXLog)
}

func initXLog() error {
	c, err := getConfig()
	if err != nil {
		return fmt.Errorf("XOne initXLog getConfig failed, err=[%v]", err)
	}
	xutil.InfoIfEnableDebug(fmt.Sprintf("XOne initXLog got config: %s", xutil.ToJsonString(c)))

	return initXLogByConfig(c)
}

func initXLogByConfig(c *Config) (err error) {
	if !xutil.DirExist(cast.ToString(c.Path)) { // 日志所在文件夹不存在则创建
		if err := os.MkdirAll(cast.ToString(c.Path), os.ModePerm); err != nil {
			return fmt.Errorf("XOne initXLogByConfig invoke os.MkdirAll failed, path=[%s], err=[%v]", cast.ToString(c.Path), err)
		}
	}

	location, _ := time.LoadLocation(cast.ToString(c.Timezone))
	clock = &myClock{location: location}

	// 创建 file writer
	logFilePath := path.Join(cast.ToString(c.Path), cast.ToString(c.Name)+".log")
	fileWriter, err := rotatelogs.New(
		logFilePath+".%Y%m%d",
		//rotatelogs.WithClock(clock), // 容器内是什么时区就用什么时区来切割, 不然时间切割日志在容器内的「当天」可能不对
		rotatelogs.WithLinkName(logFilePath),
		rotatelogs.WithMaxAge(xutil.ToDuration(c.MaxAge)),
		rotatelogs.WithRotationTime(xutil.ToDuration(c.RotateTime)),
		rotatelogs.WithRotationSize(*c.RotateSize),
		rotatelogs.WithRotationCount(*c.RotateCount),
	)
	if err != nil {
		return fmt.Errorf("XOne initXLogByConfig invoke rotatelogs.New failed, err=[%v]", err)
	}

	logrus.SetOutput(io.Discard)

	// 设置日志输出格式
	logrus.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: "2006-01-02 15:04:05.999",
		CallerPrettyfier: func(*runtime.Frame) (function string, file string) {
			return "", "" // 去掉自带的file和func字段
		},
	})

	localIP, _ := xutil.GetLocalIp()
	localIP = xutil.GetOrDefault(localIP, "0.0.0.0")

	// 自定义hook，进行日志format和打印到屏幕
	logrus.AddHook(&xLogHook{
		SuffixToIgnore:     []string{findFrameIgnoreFileName},
		AppId:              xconfig.GetServerAppId(),
		IP:                 localIP,
		Pid:                os.Getpid(),
		Console:            cast.ToBool(c.Console),
		ConsoleFormatIsRaw: cast.ToBool(c.ConsoleFormatIsRaw),
		Writer:             os.Stdout,
	})

	// file writer hook
	logrus.AddHook(&logwriter.Hook{
		Writer:    fileWriter,
		LogLevels: resolveLevels(cast.ToString(c.Level)),
	})

	l, err := logrus.ParseLevel(cast.ToString(c.Level))
	if err != nil {
		l = logrus.InfoLevel
	}
	logrus.SetLevel(l)

	return nil
}

func getConfig() (*Config, error) {
	// 获取配置
	c := &Config{}
	if err := xconfig.UnmarshalConfig(XLogConfigKey, c); err != nil {
		return nil, err
	}
	c = configMergeDefault(c)
	return c, nil
}

func resolveLevels(l string) []logrus.Level {
	levels := make([]logrus.Level, 0)
	switch strings.ToLower(l) {
	case "debug":
		levels = logrus.AllLevels[1:6] //[fatal error warning info]
	case "info":
		levels = logrus.AllLevels[1:5]
	case "warn":
		levels = logrus.AllLevels[1:4]
	case "error":
		levels = logrus.AllLevels[1:3]
	case "fatal":
		levels = logrus.AllLevels[1:2]
	default:
		levels = logrus.AllLevels[1:5] // default info 级别
	}
	return levels
}
